import React from 'react';
import { <PERSON>, Sun } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

export function DarkModeToggle() {
  const { isDarkMode, toggleDarkMode } = useAuth();

  return (
    <button
      onClick={toggleDarkMode}
      className="fixed top-4 right-4 p-3 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-xl z-50"
      aria-label="Toggle dark mode"
    >
      {isDarkMode ? (
        <Sun className="w-5 h-5 text-yellow-500 animate-spin" style={{ animationDuration: '8s' }} />
      ) : (
        <Moon className="w-5 h-5 text-blue-600 animate-pulse" />
      )}
    </button>
  );
}