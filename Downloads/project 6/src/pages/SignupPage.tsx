import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Code, Rocket } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { InputField } from '../components/InputField';
import { AnimatedButton } from '../components/AnimatedButton';

export function SignupPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const { signup } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    const success = await signup(email, password, name);
    
    if (success) {
      navigate('/dashboard');
    }
    
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900 transition-colors duration-500">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md"
      >
        {/* Logo */}
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl">
              <Rocket className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              CodingTutor.AI
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">Begin your coding adventure!</p>
        </motion.div>

        {/* Signup Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-gray-700"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <InputField
                type="text"
                placeholder="What should we call you? 👨‍💻"
                value={name}
                onChange={setName}
                required
              />
              
              <InputField
                type="email"
                placeholder="Your digital address 📧"
                value={email}
                onChange={setEmail}
                required
              />
              
              <InputField
                type="password"
                placeholder="Create your master key 🔑"
                value={password}
                onChange={setPassword}
                required
              />
            </div>

            <AnimatedButton
              onClick={() => {}}
              loading={loading}
              className="w-full"
            >
              {loading ? 'Creating magic...' : 'Create my journey ✨'}
            </AnimatedButton>
          </form>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="mt-6 text-center"
          >
            <p className="text-gray-600 dark:text-gray-400">
              Already part of our community?{' '}
              <Link
                to="/login"
                className="text-purple-600 dark:text-purple-400 hover:text-pink-600 dark:hover:text-pink-400 font-semibold transition-colors duration-200 hover:underline"
              >
                Sign in here
              </Link>
            </p>
          </motion.div>
        </motion.div>

        {/* Benefits */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-6 grid grid-cols-3 gap-4 text-center"
        >
          {[
            { icon: '🎯', text: 'Personalized' },
            { icon: '🚀', text: 'Fast Learning' },
            { icon: '🏆', text: 'Achievement' }
          ].map((item, index) => (
            <div key={index} className="text-sm text-gray-500 dark:text-gray-400">
              <div className="text-2xl mb-1">{item.icon}</div>
              <div>{item.text}</div>
            </div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
}