import React, { useState } from 'react';

interface AnimatedButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

export function AnimatedButton({ 
  onClick, 
  children, 
  variant = 'primary', 
  loading = false, 
  disabled = false,
  className = ''
}: AnimatedButtonProps) {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    if (disabled || loading) return;
    
    setIsClicked(true);
    setTimeout(() => setIsClicked(false), 200);
    onClick();
  };

  const baseClasses = `
    relative px-8 py-3 rounded-xl font-semibold text-lg transition-all duration-300 
    focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed
    transform hover:scale-105 active:scale-95 overflow-hidden
    ${isClicked ? 'animate-bounce' : ''}
  `;

  const variants = {
    primary: `
      bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700
      text-white shadow-lg hover:shadow-xl shadow-blue-500/25 hover:shadow-purple-500/25
      focus:ring-blue-500/50 dark:shadow-blue-400/25 dark:hover:shadow-purple-400/25
    `,
    secondary: `
      bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-2 border-gray-200 dark:border-gray-600
      text-gray-900 dark:text-white hover:border-gray-300 dark:hover:border-gray-500
      shadow-lg hover:shadow-xl focus:ring-gray-500/20
    `
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variants[variant]} ${className}`}
    >
      {/* Shimmer effect */}
      <div className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 transition-transform duration-1000 hover:translate-x-full" />
      
      {/* Content */}
      <span className="relative z-10 flex items-center justify-center gap-2">
        {loading && (
          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
        )}
        {children}
      </span>
    </button>
  );
}